import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, SafeAreaView, StatusBar } from 'react-native';
import FontAwesome from 'react-native-vector-icons/FontAwesome';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';

export default function LegalInfoScreen() {
    const navigation = useNavigation();
    const openLink = (url: string) => {
      navigation.navigate('Webview', { url });
    };
  return (
    <SafeAreaView style={styles.safeArea}>
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <MaterialCommunityIcons name="arrow-left" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Thông tin pháp lý</Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView style={styles.container}>
        <View style={styles.sectionBox}>
          <Text style={styles.sectionTitle}><PERSON><PERSON><PERSON> & <PERSON>ho<PERSON>n</Text>

          <TouchableOpacity style={styles.item} onPress={() => openLink('https://pay2s.vn/open-api-banking#team-sec')}>
            <View style={styles.itemIcon}>
              <MaterialCommunityIcons name="handshake" size={20} color="#308a5a" />
            </View>
            <Text style={styles.text}>Công bố hợp tác</Text>
            <MaterialCommunityIcons name="chevron-right" size={20} color="#bbb" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.item} onPress={() => openLink('https://pay2s.vn/chinh-sach-bao-mat')}>
            <View style={styles.itemIcon}>
              <MaterialCommunityIcons name="shield-check" size={20} color="#2196f3" />
            </View>
            <Text style={styles.text}>Chính sách bảo mật</Text>
            <MaterialCommunityIcons name="chevron-right" size={20} color="#bbb" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.item} onPress={() => openLink('https://pay2s.vn/thoa-thuan')}>
            <View style={styles.itemIcon}>
              <MaterialCommunityIcons name="file-document-outline" size={20} color="#ff9800" />
            </View>
            <Text style={styles.text}>Thỏa thuận sử dụng dịch vụ</Text>
            <MaterialCommunityIcons name="chevron-right" size={20} color="#bbb" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.item} onPress={() => openLink('https://pay2s.vn/tiep-nhan-xu-ly')}>
            <View style={styles.itemIcon}>
              <MaterialCommunityIcons name="message-alert-outline" size={20} color="#e74c3c" />
            </View>
            <Text style={styles.text}>Tiếp nhận & Xử lý khiếu nại</Text>
            <MaterialCommunityIcons name="chevron-right" size={20} color="#bbb" />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#308a5a'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#308a5a',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    flex: 1,
    textAlign: 'center'
  },
  headerRight: {
    width: 40
  },
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    padding: 20
  },
  sectionBox: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOpacity: 0.06,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 8,
    elevation: 3
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2c3e50',
    marginBottom: 16,
  },
  item: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  itemIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16
  },
  text: {
    fontSize: 15,
    color: '#2c3e50',
    fontWeight: '500',
    flex: 1
  },
});